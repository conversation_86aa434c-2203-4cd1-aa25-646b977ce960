/* Animación al hacer clic en proyectos */
.proyectos li a:hover {
    transform: scale(1.1);
    /* Escalar al hacer clic */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    /* Sombra al hacer clic */
}

/* Parallax en el fondo de proyectos */
.proyectos {
    background-position: center top;
    background-attachment: fixed;
    background-size: cover;
    background-image: url('imagenes/fondo-proyectos.jpg');
}

.proyectos:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('imagenes/fondo-proyectos-2.jpg');
    background-position: center top;
    background-attachment: fixed;
    background-size: cover;
    opacity: 0.3;
    z-index: 1;
    transform: translateY(-50%);
}

/* ScrollReveal para habilidades */
.habilidades {
    opacity: 0;
}

.habilidades:not(.is-visible) {
    transform: translateY(50px);
}

/* Animación de "hover" en habilidades */
.habilidades li:hover {
    background-color: #f5f5f5;
    transform: scale(1.05);
}

/* Tooltips en contacto */
.contacto li {
    position: relative;
}

.contacto li::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 10px;
    background-color: #333;
    color: #fff;
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    z-index: 10;
}

.contacto li:hover::after {
    opacity: 1;
    visibility: visible;
    bottom: 10px;
}

/* Bordes con degradados */
.proyectos li {
    border: 1px solid #ddd;
    border-radius: 5px;
    background: linear-gradient(to bottom, #fff, #f5f5f5);
}

.habilidades li {
    border: 1px solid #f0f0f0;
    border-radius: 5px;
    background: linear-gradient(to bottom, #f0f0f0, #e5e5e3);

    /* Sombras a los elementos */
    .proyectos li {
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .habilidades li {
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
    }

    .contacto li {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Ajustes adicionales */
    h1 {
        font-size: 2.5em;
    }

    p {
        font-size: 17px;
    }

    .proyectos h2 {
        font-size: 1.8em;
    }

    .habilidades h2,
    .contacto h2 {
        font-size: 1.7em;
    }

    .proyectos li img {
        height: 220px;
    }
}